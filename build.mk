SDK_LIB =$(PWD)/src/lib/sdk_lib/lib
SDK_INC_DIR = $(PWD)/src/lib/sdk_lib/include
INCLUDES = -I$(SDK_INC_DIR)

CFLAGS += $(INCLUDES) -O2 -Wall 


# NANOMSG_LIB=$(PWD)/lib/nanomsg_lib/lib
# NANOMSG_INLCUDE=$(PWD)/lib/nanomsg_lib/include
# CFLAGS += -I$(NANOMSG_INLCUDE)


LIB_PATH +=-L$(PWD)/src/lib
LIB_PATH +=-L$(PWD)/src/sdk_lib/lib
# LIB_PATH +=-L$(NANOMSG_LIB)

LIBS := -lpthread -lm -ldl 


#SDK_LIBDEPS = --start-group $(MPI_LIBS) -Wl,--end-group

LIBS +=$(SDK_LIB)/libss_mpi.a
LIBS +=$(SDK_LIB)/libsecurec.a

LIBS += -L./src/lib -lUSBLib -lusb-1.0 -lnanomsg
LIBS += -lstdc++
#LIBS += -lnanomsg



