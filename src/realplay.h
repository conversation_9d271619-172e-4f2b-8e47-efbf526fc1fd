#ifndef REALPLAY_H
#define REALPLAY_H

#ifdef __cplusplus
extern "C"{
#endif

#include <stdlib.h>
#include <stdio.h>
#if defined(WIN32) || defined(WIN64)
#include <windows.h>
#else
#include <pthread.h>
#include <sys/time.h>
#include <signal.h>
#include <unistd.h>
#endif
#include <stdint.h>

#include <stdbool.h>
#include "USBLib_wrapper.h"



int start_dahua_proc(fUSBSDKDataCallBack callback, const char *update_file_path);
int stop_dahua_proc();
int cleanup_dahua();
#ifdef __cplusplus
}
#endif

#endif

