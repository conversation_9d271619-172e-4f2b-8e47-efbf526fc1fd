#ifndef USBLIB_WRAPPER_H_
#define USBLIB_WRAPPER_H_

#include "USBLib_origin_proxy.h"  

#ifdef __cplusplus
extern "C" {
#endif


typedef struct
{
    int nDeviceAddress;
    unsigned char deviceName[256];
    unsigned char deviceSerialNumber;
}USBSDK_DeviceInfo;

#define DWORD		unsigned long
#define BOOL        int
#define USBSDK_API
#define CALLMETHOD


typedef enum USBSDK_RADIOMETRY_METERTYPE {
	USBSDK_RADIOMETERY_SPOTMETER,                 //�����
	USBSDK_RADIOMETERY_LINEMETER,				  //�߲���
	USBSDK_RADIOMETERY_AREAMETER				  //�������
}USBSDK_RADIOMETRY_METERTYPE;

/// �㣬8�ֽ�
typedef struct USBSDK_Point
{
	int x;
	int y;
}USBSDK_Point;

/* �������� 68�ֽ� */
typedef struct USBSDK_Area
{
	unsigned int  pointNumber;
	USBSDK_Point   points[8];
}USBSDK_Area;

/// ֱ�ߣ�16�ֽ�
typedef struct USBSDK_Line
{
	USBSDK_Point start;
	USBSDK_Point end;
}USBSDK_Line;

/* �㡢�ߡ��������� 68�ֽ� */
typedef union
{
	USBSDK_Point           spotPoint;	    /* ��������� */
	USBSDK_Area			   areaRegion;      /* ������·�Χ */
	USBSDK_Line			   linePostion;     /* �߲���λ�� */
}USBSDK_RegionConfig;

///�������������
typedef enum USBSDK_RADIOMETRY_AREASUBTYPE {
	USBSDK_RADIOMETERY_AREAPOLYGON,            //�����
	USBSDK_RADIOMETERY_AREARECT,               //��������
	USBSDK_RADIOMETERY_AREAELLIPSE             //��Բ����
}USBSDK_RADIOMETRY_AREASUBTYPE;

/* ���¾ֲ����� 32�ֽ� */
typedef struct USBSDK_LocalParam
{
	unsigned int  enable;   /* ʹ�� */
	float		  objectDistance;
	float		  refalectedTemp;
	float		  objectEmissivity;
	unsigned int  res[4];
}USBSDK_LocalParam;

typedef struct USBSDK_RuleInfo
{
	unsigned int                        ruleId;			/* �������� */
	unsigned int                        enable;			/* ʹ���� */
	USBSDK_RADIOMETRY_METERTYPE			meterType;		/* ���������� */
	USBSDK_LocalParam					localParam;		 /* �ֲ����� */
	USBSDK_RegionConfig					regionConfig;	 /* �㡢�ߡ��������� */
	USBSDK_RADIOMETRY_AREASUBTYPE		subType;		 /* ������������ͣ�ֻ�������������ʱ��Ч */
	char								ruleName[16];	/* �������� */
	unsigned int                        res[3];
}USBSDK_RuleInfo;

typedef struct USBSDK_Rules
{
	unsigned int				count;			/* ���µ��������� */
	USBSDK_RuleInfo				Rules[12];		/* ����Ĺ��� */
}USBSDK_Rules;


typedef struct USBSDK_RuleTemper
{
	USBSDK_RADIOMETRY_METERTYPE meterType;	/* ���������� */
	unsigned int  ruleId;                   /* �������� */
	unsigned int  resultTypeMask;
	float temperatureAve;                 /* ƽ���¶� */
	float temperatureStd;                 /* ��׼�����¶� */
	float temperatureMax;                 /* ����¶� */
	float temperatureMin;                 /* ����¶� */
	float temperatureMid;                 /* �м��¶� */
	USBSDK_Point   hotPoint;				        /* �ȵ�����[0,8192) */
	USBSDK_Point   coldPoint;			            /* �������[0,8192)	*/
	unsigned int  res[4];
} USBSDK_RuleTemper;


//typedef void* USBSDK_RuleTemper; 
typedef void(*fUSBSDKDataCallBack)(unsigned char* pYUVBuffer, int nYUVBufLen, unsigned char* pRAWBuffer, int nRawBufLen, int nWidth, int nHeight, unsigned long dwUser);

// 重声明函数（关键修改点）
BOOL USBSDK_GetThermalRules(unsigned int ruleId, USBSDK_RuleTemper* pRulesTemperInfo); // 将引用&改为指针*
BOOL USBSDK_UpdateMainProgram(char* pFilePath); 
USBSDK_API int CALLMETHOD USBSDK_GetDevInfoList(USBSDK_DeviceInfo* devInfoList);
USBSDK_API BOOL CALLMETHOD USBSDK_Open(USBSDK_DeviceInfo* devInfo);
USBSDK_API BOOL CALLMETHOD USBSDK_Close();
USBSDK_API BOOL CALLMETHOD USBSDK_StartRealPlay(fUSBSDKDataCallBack cbTIUSBDataCallBack,DWORD dwUser);
USBSDK_API BOOL CALLMETHOD USBSDK_SetThermalRules(USBSDK_Rules pRules);
USBSDK_API BOOL CALLMETHOD USBSDK_SetFineDigtalZoom(float fFineMagni);
USBSDK_API BOOL CALLMETHOD USBSDK_GetFineDigtalZoom(float* fFineMagni);

#ifdef __cplusplus
}
#endif

#endif
