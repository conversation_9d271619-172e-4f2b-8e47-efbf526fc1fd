include ../Makefile.param

SMP_SRCS := $(wildcard *.c)
SMP_SRCS += $(wildcard ./ipc/*.c)
SMP_SRCS += $(wildcard ./ipc/infrared/*.c)
SMP_SRCS += $(wildcard ./ini/*.c)
CFLAGS += -I./ipc -I./ipc/infrared -I./ipc/nanomsg/include  -I./ini


TARGET := infra_uvc


MPP_CFLAGS+=-DGIT_SHA1="$(shell git log --format='[sha1]:%h [author]:%cn [time]:%ci [commit]:%s [branch]:%d' -1)"
MPP_CFLAGS+=-DGIT_DESC="$(shell git describe --tags --always --dirty)"

LD_FLAGS += -L./lib -lUSBLib -lusb-1.0 -lnanomsg

TARGET_PATH := $(PWD)

## enable acquire infra frame from dahua sdk.
MPP_CFLAGS += -DENABLE_DAHUA_SDK

include $(PWD)/../$(ARM_ARCH)_$(OSTYPE).mak
