#include "realplay.h"
//If the interface returns a failure, the error code can be obtained through the USBSDK_GetLastError interface.
//#include "USBLib.h"
#include "USBLib_wrapper.h"
#include <stdio.h>
#include <pthread.h>
#include <unistd.h>
#include "infra_message.h"
#include "dictionary.h"
#include "iniparser.h"
#include "confighelper.h"



static int g_exit_flag = 0;
static pthread_t tmp_tid;
bool bReceiveData = false;

#define INFRA_SECTION "infra"
#define CFG_FILE_NAME "/app/app/configtracker.ini"
#define BRIGHTNESS_KEY "infra:brightness"
#define CONTRAST_KEY "infra:contrast"
#define GAIN_KEY "infra:gain"
#define PSEUDO_KEY "infra:pseudo_color"
#define LCE_KEY "infra:lce"
#define ALAMR_SWITCH "infra:alarm_switch"
#define ALARM_TEMPERATURE "infra:alarm_temperature"


#define DEFAULT_BRIGHTNESS 50
#define DEFAULT_CONTRAST   50
#define DEFAULT_GAIN 		1
#define DEFAULT_COLOR 		0
#define DEFAULT_LCE 		64

static int brightness;
static int contrast;
static int gain;
static int pseudo_color;
static int alarm_switch;
static int alarm_temperature;
static int lce;
static float zoom_factor = 1.0;

int load_cfg_from_ini(const char *cfg_file_path)
{
    dictionary  *   ini ;

    /* Some temporary variables to hold query results */
    // int             b ;
    // int             i ;
    // double          d ;
    // const char  *   s ;

    ini = iniparser_load(cfg_file_path);
    if (ini==NULL) {
        fprintf(stderr, "cannot parse file: %s\n", cfg_file_path);
        return -1 ;
    }

    brightness = iniparser_getint(ini, BRIGHTNESS_KEY, DEFAULT_BRIGHTNESS);
    printf("brightness:       [%d]\n", brightness);

	contrast = iniparser_getint(ini, CONTRAST_KEY, DEFAULT_CONTRAST);
    printf("contrast:       [%d]\n", contrast);

	gain = iniparser_getint(ini, GAIN_KEY, DEFAULT_GAIN);
    printf("gain:       [%d]\n", gain);

	pseudo_color = iniparser_getint(ini, PSEUDO_KEY, DEFAULT_COLOR);
    printf("pseudo_color:       [%d]\n", pseudo_color);

	lce = iniparser_getint(ini, LCE_KEY, DEFAULT_LCE);
	printf("lce:       [%d]\n", lce);


	alarm_switch = iniparser_getint(ini, ALAMR_SWITCH, 0);
    printf("alarm_switch:       [%d]\n", alarm_switch);

	alarm_temperature = iniparser_getint(ini, ALARM_TEMPERATURE, 100);
    printf("alarm_temperature:       [%d]\n", alarm_temperature);

	

	if (USBSDK_SetBrightness(brightness))
	{
		printf("set brightness : %d\n", brightness);
	}

	if (USBSDK_SetContrast(contrast))
	{
		printf("set Contrast : %d\n", contrast);
	}

	if (USBSDK_SetGainAuto(gain))
	{
		printf("set GainAuto : %d\n", gain);
	}

	if (USBSDK_SetColor(pseudo_color))
	{
		printf("set color : %d\n", pseudo_color);
	}

    iniparser_freedict(ini);
    return 0 ;
}


void get_default_value()
{
	int value;
	printf("\n\n\n\n");
	if (USBSDK_GetBrightness(&value))
	{
		printf("brightness : %d\n", value);
	}

	if (USBSDK_GetContrast(&value))
	{
		printf("Contrast : %d\n", value);
	}

	if (USBSDK_GetGainAuto(&value))
	{
		printf("GainAuto : %d\n", value);
	}


	if (USBSDK_GetLCEValue(&value))
	{
		printf("LCE : %d\n", value);
	}

	if (USBSDK_GetColor(&value)){
		printf("color : %d\n", value);
	}
}

#define MEASURE_RECT_WIDTH 	8191
#define MEASURE_RECT_HEIGHT 8191

#define MEASURE_CENTER_RECT_WIDTH  2
#define MEASURE_CENTER_RECT_HEIGHT 2



int start_dahua_proc(fUSBSDKDataCallBack callback, const char *update_file_path)
{
    printf("start_dahua_proc\n");
    if (!USBSDK_Init())
    {
        printf("USBSDK_Init Failed, ErrCode: %d\n", USBSDK_GetLastError());
        return -1;
    }
    
    DWORD dwRet = USBSDK_GetSdkVersion();
    printf("USBSDK_GetSdkVersion: %ld\n", dwRet);

    USBSDK_DeviceInfo devInfoList;
    if (USBSDK_GetDevInfoList(&devInfoList) <= 0)
    {
        printf("USBSDK_GetDevInfoList Failed, ErrCode: %d\n", USBSDK_GetLastError());
        return -1;
    }
    
    if (!USBSDK_Open(&devInfoList))
    {
        printf("USBSDK_Open Failed, ErrCode: %d\n", USBSDK_GetLastError());
        return -1;
    }

	char sofware_version[256] = {0};
	if (USBSDK_GetSoftwareVersion(sofware_version, 256))
	{
		printf("sofware_version : %s\n", sofware_version);
	}

    if (update_file_path != NULL && strlen(update_file_path) > 0)
    {
        printf("Upgrading firmware with file: %s\n", update_file_path);
        if (!USBSDK_UpdateMainProgram(update_file_path))
        {
            printf("USBSDK_UpdateMainProgram Failed, ErrCode: %d\n", USBSDK_GetLastError());
			if (!USBSDK_Close())
			{
				printf("USBSDK_Close Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
            return -1;
        }
        else
        {
            printf("USBSDK_UpdateMainProgram Success\n");
			if (!USBSDK_Close())
			{
				printf("USBSDK_Close Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
            return 0; 
        }
    }

    
    if (!USBSDK_OpenThermalFunction())
    {
        printf("USBSDK_OpenThermalFunction Failed\n");
    }

	//get_default_value();
	load_cfg_from_ini(CFG_FILE_NAME);
	
    DWORD dw = NULL;
    BOOL bRet = USBSDK_StartRealPlay(callback,dw);
    if (!bRet)
    {
        printf("USBSDK_StartRealPlay Failed, ErrCode: %d\n", USBSDK_GetLastError());
        return -1;
    }


    printf("--------tmpRules----------\n");
    USBSDK_Rules tmpRules = {0};												//Maximum support for 12 temperature measurement rules
	tmpRules.count = 2;
	// //Point
	// tmpRules.Rules[0].enable = true;
	// tmpRules.Rules[0].ruleId = 1;
	// tmpRules.Rules[0].meterType = USBSDK_RADIOMETERY_SPOTMETER;
	// //local param
	// tmpRules.Rules[0].localParam.enable = 1;										//if enable is 0��no write localParam
	// tmpRules.Rules[0].localParam.objectDistance = 10;
	// tmpRules.Rules[0].localParam.refalectedTemp = 25;
	// tmpRules.Rules[0].localParam.objectEmissivity = 0.98;
	// //The coordinate system is 8192, with the top left corner as the origin
	// tmpRules.Rules[0].regionConfig.spotPoint.x = 320;
	// tmpRules.Rules[0].regionConfig.spotPoint.y = 250;

	// //Line
	// tmpRules.Rules[1].enable = true;
	// tmpRules.Rules[1].ruleId = 2;
	// tmpRules.Rules[1].meterType = USBSDK_RADIOMETERY_LINEMETER;
	// //The coordinate system is 8192, with the top left corner as the origin
	// tmpRules.Rules[1].regionConfig.linePostion.start.x = 30;
	// tmpRules.Rules[1].regionConfig.linePostion.start.y = 20;
	// tmpRules.Rules[1].regionConfig.linePostion.end.x = 300;
	// tmpRules.Rules[1].regionConfig.linePostion.end.y = 200;

	//Rect
	tmpRules.Rules[0].enable = true;
	tmpRules.Rules[0].ruleId = 1;
	tmpRules.Rules[0].meterType = USBSDK_RADIOMETERY_AREAMETER;
	tmpRules.Rules[0].subType = USBSDK_RADIOMETERY_AREARECT;
	tmpRules.Rules[0].regionConfig.areaRegion.pointNumber = 4;
	tmpRules.Rules[0].regionConfig.areaRegion.points[0].x = 0;
	tmpRules.Rules[0].regionConfig.areaRegion.points[0].y = 0;
	tmpRules.Rules[0].regionConfig.areaRegion.points[1].x = MEASURE_RECT_WIDTH;
	tmpRules.Rules[0].regionConfig.areaRegion.points[1].y = 0;
	tmpRules.Rules[0].regionConfig.areaRegion.points[2].x = 0;
	tmpRules.Rules[0].regionConfig.areaRegion.points[2].y = MEASURE_RECT_HEIGHT;
	tmpRules.Rules[0].regionConfig.areaRegion.points[3].x = MEASURE_RECT_WIDTH;
	tmpRules.Rules[0].regionConfig.areaRegion.points[3].y = MEASURE_RECT_HEIGHT;


	//Rect
	tmpRules.Rules[1].enable = true;
	tmpRules.Rules[1].ruleId = 2;
	tmpRules.Rules[1].meterType = USBSDK_RADIOMETERY_AREAMETER;
	tmpRules.Rules[1].subType = USBSDK_RADIOMETERY_AREARECT;
	tmpRules.Rules[1].regionConfig.areaRegion.pointNumber = 4;
	tmpRules.Rules[1].regionConfig.areaRegion.points[0].x = MEASURE_RECT_WIDTH /2 - MEASURE_CENTER_RECT_WIDTH /2;
	tmpRules.Rules[1].regionConfig.areaRegion.points[0].y = MEASURE_RECT_HEIGHT /2 - MEASURE_CENTER_RECT_HEIGHT /2;;
	tmpRules.Rules[1].regionConfig.areaRegion.points[1].x = MEASURE_RECT_WIDTH /2 + MEASURE_CENTER_RECT_WIDTH /2;
	tmpRules.Rules[1].regionConfig.areaRegion.points[1].y = MEASURE_RECT_HEIGHT /2 - MEASURE_CENTER_RECT_HEIGHT /2;
	tmpRules.Rules[1].regionConfig.areaRegion.points[2].x = MEASURE_RECT_WIDTH /2 - MEASURE_CENTER_RECT_WIDTH /2;;
	tmpRules.Rules[1].regionConfig.areaRegion.points[2].y = MEASURE_RECT_HEIGHT /2 + MEASURE_CENTER_RECT_HEIGHT /2;
	tmpRules.Rules[1].regionConfig.areaRegion.points[3].x = MEASURE_RECT_WIDTH /2 + MEASURE_CENTER_RECT_WIDTH /2;
	tmpRules.Rules[1].regionConfig.areaRegion.points[3].y = MEASURE_RECT_HEIGHT /2 + MEASURE_CENTER_RECT_HEIGHT /2;

	// //Circle
	// tmpRules.Rules[3].enable = true;
	// tmpRules.Rules[3].ruleId = 4;
	// tmpRules.Rules[3].meterType = USBSDK_RADIOMETERY_AREAMETER;
	// tmpRules.Rules[3].subType = USBSDK_RADIOMETERY_AREAELLIPSE;
	// tmpRules.Rules[3].regionConfig.areaRegion.pointNumber = 4;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[0].x = 268;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[0].y = 387;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[1].x = 378;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[1].y = 387;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[2].x = 378;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[2].y = 487;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[3].x = 268;
	// tmpRules.Rules[3].regionConfig.areaRegion.points[3].y = 487;



    if (!USBSDK_SetThermalRules(tmpRules))
    {
        printf("USBSDK_SetThermalRules Failed\n");
        if (!USBSDK_Close())
        {
            printf("USBSDK_Close Failed\n");
        }
        return -1;
    }

	unsigned int count = 0;
    sleep(1);
    while(!g_exit_flag)
    {
		count ++;

		if (count % 10 == 0)
		{
			USBSDK_RuleTemper pRulesTemperInfo;
			BOOL bfullRet = USBSDK_GetThermalRules(1, &pRulesTemperInfo);
            if (!bfullRet)
            {
                int nError = USBSDK_GetLastError();	
                printf("USBSDK_GetThermalRules Failed, rule ID: %d, errorCode: %d\n",3,nError);
            }
            else
            {
                // printf("rect:8191*8191 rule ID: %d, Max Temperature: %.2f, Min Temperature: %.2f, Avg Temperature: %.2f\n", 
                //     1, pRulesTemperInfo.temperatureMax, 
                //     pRulesTemperInfo.temperatureMin, 
                //     pRulesTemperInfo.temperatureAve);
            }


			USBSDK_RuleTemper pcenter_RulesTemperInfo;
			BOOL bcenterRet = USBSDK_GetThermalRules(2, &pcenter_RulesTemperInfo);
            if (!bcenterRet)
            {
                int nError = USBSDK_GetLastError();	
                printf("USBSDK_GetThermalRules Failed, rule ID: %d, errorCode: %d\n",3,nError);
            }
            else
            {
                // printf("rect:2*2 rule ID: %d, Max Temperature: %.2f, Min Temperature: %.2f, Avg Temperature: %.2f\n", 
                //     2, pcenter_RulesTemperInfo.temperatureMax, 
                //     pcenter_RulesTemperInfo.temperatureMin, 
                //     pcenter_RulesTemperInfo.temperatureAve);
            }
			
			if(bfullRet && bcenterRet)
			{
				airvisen_send_temperautre_point_command((int)(pRulesTemperInfo.temperatureMax *10),
				pRulesTemperInfo.hotPoint.x, 
				pRulesTemperInfo.hotPoint.y,
				(int)(pRulesTemperInfo.temperatureMin *10),
				pRulesTemperInfo.coldPoint.x, 
				pRulesTemperInfo.coldPoint.y,
				(int)(pcenter_RulesTemperInfo.temperatureAve*10));
			}


		}
		

		//set color 
		char data[256] = {0};
		int command = airvisen_recv_infra_message(data, sizeof(data));
		if (command == INFRA_CORRECT_SHUTTER)
		{
			if (!USBSDK_ShutDown())
			{
				printf("USBSDK_ShutDown Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			else{
				printf("USBSDK_ShutDown ok\n");
			}
			continue;
		}
		else if (command == INFRA_ADD_CONTRAST)
		{
			if (contrast + 1 > 100)
			{
				continue;
			}
			
			contrast = contrast + 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", contrast);
			if(write_to_ini(CFG_FILE_NAME, INFRA_SECTION, CONTRAST_KEY, tmp) != 0){
				printf("write to contrast cfg failed\n");
			}

			if (!USBSDK_SetContrast(contrast))
			{
				printf("USBSDK_SetContrast Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}else{
				printf("USBSDK_SetContrast ok\n");
			}
			continue;
		}
		else if (command == INFRA_SUB_CONTRAST)
		{
			if (contrast - 1 < 0)
			{
				continue;
			}
			
			contrast = contrast - 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", contrast);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION, CONTRAST_KEY, tmp);
			if (!USBSDK_SetContrast(contrast))
			{
				printf("USBSDK_SetContrast Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			else{
				printf("USBSDK_SetContrast ok\n");
			}
			continue;
		}
		else if (command == INFRA_ADD_BRIGHTNESS)
		{
			if (brightness + 1 > 100)
			{
				continue;
			}
			
			brightness = brightness + 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", brightness);
			if(write_to_ini(CFG_FILE_NAME, INFRA_SECTION, BRIGHTNESS_KEY, tmp) != 0){
				printf("write to brightness cfg failed\n");
			}
			if (!USBSDK_SetBrightness(brightness))
			{
				printf("USBSDK_SetBrightness Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}else{
				printf("USBSDK_SetBrightness ok\n");
			}
			continue;
		}
		else if (command == INFRA_SUB_BRIGHTNESS)
		{
			if (brightness - 1 < 0)
			{
				continue;
			}
			
			brightness = brightness - 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", brightness);
			write_to_ini(CFG_FILE_NAME,  INFRA_SECTION, BRIGHTNESS_KEY, tmp);
			if (!USBSDK_SetBrightness(brightness))
			{
				printf("USBSDK_SetBrightness Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			continue;
		}
		else if (command == INFRA_RESET_BRI_CONTRAST_DEFAULT)
		{
			brightness = DEFAULT_BRIGHTNESS;
			
			char tmp[32] = {0};
			sprintf(tmp, "%d", brightness);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION, BRIGHTNESS_KEY, tmp);
			if (!USBSDK_SetBrightness(brightness))
			{
				printf("USBSDK_SetBrightness Failed, ErrCode: %d\n", USBSDK_GetLastError());
				continue;
			}else{
				printf("USBSDK_SetBrightness ok\n");
			}
			contrast = DEFAULT_CONTRAST;
			memset(tmp,0,32);
			sprintf(tmp, "%d", contrast);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION,  CONTRAST_KEY, tmp);
			if (!USBSDK_SetContrast(contrast))
			{
				printf("USBSDK_SetContrast Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}else{
				printf("USBSDK_SetContrast ok\n");
			}
			continue;
		}
		else if (command == INFRA_SET_GAINAUTO)
		{
			airvisen_set_gainauto * p = (airvisen_set_gainauto *)data;
			gain = p->value;
			char tmp[32] = {0};
			sprintf(tmp, "%d", gain);
			printf("\n\n gain: %d\n", gain);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION,  GAIN_KEY, tmp);
			if (!USBSDK_SetLCEValue(gain*18))
			{
				printf("USBSDK_SetLCEValue Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			else{
				printf("USBSDK_SetLCEValue ok\n");
			}
			continue;
		}
		else if (command == INFRA_SET_COLOR)
		{
			//大华机芯
			// 			0-白热，1-黑热，2-聚变，3-彩虹，4-金秋，5-午日，
			// 6-铁红，7-琥珀，8-玉石，9-夕阳，10-冰火，11-油画，
			// 12-石榴，13-翡翠，14-春，15-夏，16-秋，17-冬，18热检测，19-极光

			//天进
			// 0x00：白热（默认）
			// 0x01：黑热
			// 0x02：彩虹
			// 0x03：高对比度彩虹
			// 0x04：铁红
			// 0x05：熔岩
			// 0x06：天空
			// 0x07：中灰
			// 0x08：灰红
			// 0x09：紫橙
			// 0x0B：警示红
			// 0x0C：冰火
			airvisen_infra_set_color_s * p = (airvisen_infra_set_color_s *)data;
			printf("pseudo_color: %d \n", p->color);
			pseudo_color =  p->color;
			int real_color = pseudo_color;
			char tmp[32] = {0};
			sprintf(tmp, "%d", pseudo_color);
			write_to_ini(CFG_FILE_NAME,  INFRA_SECTION, PSEUDO_KEY, tmp);
			// switch (pseudo_color)
			// {
			// case 0:
			// 	real_color = 0; break;
			// case 1:
			// 	real_color = 1; break;
			// case 2:
			// 	real_color = 3; break;
			// case 3: 
			// 	real_color = 3; break;
			// case 4:
			// 	real_color = 6; break;
			// case 5: 
			// 	real_color = 7; break;
			// case 6:
			// 	real_color = 15; break;
			// case 7: 
			// 	real_color = 8; break;
			// case 8:
			// 	real_color = 12; break;
			// case 9:
			// 	real_color = 9; break;
			// case 0xb:
			// 	real_color = 12; break;
			// case 0xc:
			// 	real_color = 10; break;
			// default:
			// 	real_color = 0;
			// 	break;
			// }

			if (!USBSDK_SetColor(pseudo_color))
			{
				printf("USBSDK_SetColor Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			else{
				printf("USBSDK_SetColor ok\n");
			}
			continue;
		}
		else if (command == INFRA_ZOOM_STOP)
		{
			zoom_factor = 1.0;
			printf("\n\n digital zoom: %f\n", zoom_factor);
			//write_to_ini(CFG_FILE_NAME, INFRA_SECTION, "DIGITAL_ZOOM_KEY", tmp);
			if (!USBSDK_SetFineDigtalZoom(zoom_factor))
			{
				printf("USBSDK_SetFineDigtalZoom  #1 Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			else{
				printf("USBSDK_SetFineDigtalZoom ok\n");
			}
			continue;
		}
		else if (command == INFRA_ZOOM_PLUS || command == INFRA_ZOOM_BIG_PLUS)
		{

			if(zoom_factor < 8.0)
			{
				zoom_factor = zoom_factor + 0.1;
			}

			
			printf("\n\n digital zoom: %d\n", zoom_factor);
			
			if (!USBSDK_SetFineDigtalZoom(zoom_factor))
			{
				printf("USBSDK_SetFineDigtalZoom #2 Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			else
			{
				printf("USBSDK_SetFineDigtalZoom ok\n");
			}
			continue;
		}
		else if (command == INFRA_ZOOM_MINUS || command == INFRA_ZOOM_BIG_MINUS)
		{
			if (zoom_factor > 1.0)
			{
				zoom_factor = zoom_factor - 0.1;
			}
			printf("\n\n digital zoom: %f\n", zoom_factor);
			if (!USBSDK_SetFineDigtalZoom(zoom_factor))
			{
				printf("USBSDK_SetFineDigtalZoom #3 Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}
			else
			{
				printf("USBSDK_SetFineDigtalZoom ok\n");
			}
			continue;
		}
		else if (command == INFRA_ZOOM_SET)
		{
			
			airvisen_set_digtal_zoom * p = (airvisen_set_digtal_zoom *)data;
			
			// float fFineMagni = 0.0;
			// if(!USBSDK_GetFineDigtalZoom(&fFineMagni))
			// {
			// 	printf("USBSDK_GetFineDigtalZoom Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }else{
			// 	printf("USBSDK_GetFineDigtalZoom ok, fFineMagni: %f\n", fFineMagni);
			// }

			zoom_factor = p->value/10.0;
			printf("\n\n digital zoom: %f\n", zoom_factor);

			if (!USBSDK_SetFineDigtalZoom(zoom_factor))
			{
				printf("USBSDK_SetFineDigtalZoom #4 Failed, ErrCode: %d\n", USBSDK_GetLastError());
			}else{
				printf("USBSDK_SetFineDigtalZoom ok\n");
			}
			continue;
		
		}

		//sleep 50ms
        usleep(50000);
    }


	printf("thread exit...\n");
    return 0;
}


int stop_dahua_proc()
{
	g_exit_flag = 1;
}


int cleanup_dahua()
{
	printf("USBSDK_CloseRealPlay\n");
    if (!USBSDK_CloseRealPlay())
	{
		printf("USBSDK_CloseRealPlay Failed, ErrCode: %d\n", USBSDK_GetLastError());
	}
	
	printf("USBSDK_Close\n");
	if (!USBSDK_Close())
	{
		printf("USBSDK_Close Failed, ErrCode: %d\n", USBSDK_GetLastError());
	}
}



