# libusb-1.0.la - a libtool library file
# Generated by libtool (GNU libtool) 2.4.6 Debian-2.4.6-15build2
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libusb-1.0.so.0'

# Names of this library.
library_names='libusb-1.0.so.0.3.0 libusb-1.0.so.0 libusb-1.0.so'

# The name of the static archive.
old_library='libusb-1.0.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' -pthread'

# Libraries that this one depends upon.
dependency_libs=' -lpthread'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libusb-1.0.
current=3
age=3
revision=0

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/home/<USER>/USBSDK/00-libusb-master/liblib/lib'
