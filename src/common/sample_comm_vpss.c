/*
  Copyright (c), 2001-2022, Shenshu Tech. Co., Ltd.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/time.h>

#include "sample_comm.h"

#define VPSS_DEFAULT_WIDTH  3840
#define VPSS_DEFAULT_HEIGHT 2160

td_void sample_comm_vpss_get_default_grp_attr(ot_vpss_grp_attr *grp_attr)
{
    grp_attr->nr_en                     = TD_TRUE;
    grp_attr->ie_en                     = TD_FALSE;
    grp_attr->dci_en                    = TD_FALSE;
    grp_attr->buf_share_en              = TD_FALSE;
    grp_attr->mcf_en                    = TD_FALSE;
    grp_attr->max_width                 = VPSS_DEFAULT_WIDTH;
    grp_attr->max_height                = VPSS_DEFAULT_HEIGHT;
    grp_attr->max_dei_width             = 0;
    grp_attr->max_dei_height            = 0;
    grp_attr->dynamic_range             = OT_DYNAMIC_RANGE_SDR8;
    grp_attr->pixel_format              = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
    grp_attr->dei_mode                  = OT_VPSS_DEI_MODE_OFF;
    grp_attr->buf_share_chn             = OT_VPSS_CHN0;
    grp_attr->buf_share_chn             = OT_VPSS_CHN1;
    grp_attr->buf_share_chn             = OT_VPSS_CHN2;
    grp_attr->nr_attr.nr_type           = OT_VPSS_NR_TYPE_VIDEO_NORM;
    grp_attr->nr_attr.compress_mode     = OT_COMPRESS_MODE_FRAME;
    grp_attr->nr_attr.nr_motion_mode    = OT_VPSS_NR_MOTION_MODE_NORM;
    grp_attr->frame_rate.src_frame_rate = -1;
    grp_attr->frame_rate.dst_frame_rate = -1;
}

td_void sample_comm_vpss_get_default_chn_attr(ot_vpss_chn_attr *chn_attr)
{
    chn_attr->mirror_en                 = TD_FALSE;
    chn_attr->flip_en                   = TD_FALSE;
    chn_attr->border_en                 = TD_FALSE;
    chn_attr->width                     = VPSS_DEFAULT_WIDTH;
    chn_attr->height                    = VPSS_DEFAULT_HEIGHT;
#ifdef LOAD_CLUT4_BMP_ENABLE
    chn_attr->depth                     = 0;
#else
    chn_attr->depth                     = 5;
#endif
    chn_attr->chn_mode                  = OT_VPSS_CHN_MODE_USER;
    chn_attr->video_format              = OT_VIDEO_FORMAT_LINEAR;
    chn_attr->dynamic_range             = OT_DYNAMIC_RANGE_SDR8;
    chn_attr->pixel_format              = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
    chn_attr->compress_mode             = OT_COMPRESS_MODE_NONE;//OT_COMPRESS_MODE_SEG;
    chn_attr->aspect_ratio.mode         = OT_ASPECT_RATIO_NONE;
    chn_attr->frame_rate.src_frame_rate = -1;
    chn_attr->frame_rate.dst_frame_rate = -1;
}


// static td_s32 sample_common_vpss_start_chn_IR(ot_vpss_grp grp, const td_bool *chn_enable,
//                                             ot_vpss_chn_attr *chn_attr, td_u32 chn_array_size)
// {
//     ot_vpss_chn vpss_chn;
//     td_s32 ret, i;

//     for (i = 0; i < (td_s32)chn_array_size; ++i) {
//         if (chn_enable[i] == TD_TRUE) {
//             vpss_chn = i;

//             if (vpss_chn == 0)
//             {
//                 chn_attr->width  = 1920;
//                 chn_attr->height = 1080;
//             }
//             else if (vpss_chn == 1)
//             {
//                 chn_attr->width  = 640;
//                 chn_attr->height = 512;
//             }
//             else if (vpss_chn == 2)
//             {
//                 chn_attr->width  = 416;
//                 chn_attr->height = 416;
//             }

//             ret = ss_mpi_vpss_set_chn_attr(grp, vpss_chn, chn_attr);
//             if (ret != TD_SUCCESS) {
//                 //sample_print("ss_mpi_vpss_set_chn_attr failed with %#x  %d--%d\n", ret,&chn_attr[vpss_chn].width,&chn_attr[vpss_chn].height);
//                 sample_print("ss_mpi_vpss_set_chn_attr failed with %#x  %d--%d\n", ret,chn_attr->width,chn_attr->height);
//                 goto disable_chn;
//             }
//             ret = ss_mpi_vpss_enable_chn(grp, vpss_chn);
//             if (ret != TD_SUCCESS) {
//                 sample_print("ss_mpi_vpss_enable_chn failed with %#x\n", ret);
//                 goto disable_chn;
//             }
//         }
//     }
//     return TD_SUCCESS;

// disable_chn:
//     for (i = i - 1; i >= 0; i--) {
//         if (chn_enable[i] == TD_TRUE) {
//             vpss_chn = i;
//             ret = ss_mpi_vpss_disable_chn(grp, vpss_chn);
//             if (ret != TD_SUCCESS) {
//                 sample_print("ss_mpi_vpss_disable_chn failed with %#x!\n", ret);
//             }
//         }
//     }
//     return TD_FAILURE;
// }

//UVC 接入红外图像 VPSS 通道创建
static td_s32 sample_common_vpss_start_chn_IR(ot_vpss_grp grp, const td_bool *chn_enable,
                                            ot_vpss_chn_attr *chn_attr, td_u32 chn_array_size)
{
    ot_vpss_chn vpss_chn;
    td_s32 ret, i;
    ot_vpss_ext_chn_attr vpss_ext_chn_PIP_attr;
    ot_vpss_ext_chn_attr vpss_ext_chn_VENC_attr;

    for (i = 0; i < (td_s32)chn_array_size; ++i) {
        if (chn_enable[i] == TD_TRUE) {
            vpss_chn = i;

            if (vpss_chn == 0)              //录像通道      本来规划是做1280*1024通道，但因通道1080P做了放大，这个通道不支持同时放大，先开640*512  
            {
                chn_attr->width  = 640; //1280
                chn_attr->height = 512; //1024
            }
            else if (vpss_chn == 1)         //PIP 画中画 大图通道
            {
                chn_attr->width  = 1280;//1920;
                chn_attr->height = 1024;//1080;
            }
            else if (vpss_chn == 2)         //Object Detection  检测算法取图通道
            {
                chn_attr->width  = 640; 
                chn_attr->height = 512;
            }
            else if (vpss_chn == 3)         //Object Tracking   跟踪算法取图通道
            {
                chn_attr->width  = 640;
                chn_attr->height = 512;
            }

            ret = ss_mpi_vpss_set_chn_attr(grp, vpss_chn, chn_attr);
            if (ret != TD_SUCCESS) {
                //sample_print("ss_mpi_vpss_set_chn_attr failed with %#x  %d--%d\n", ret,&chn_attr[vpss_chn].width,&chn_attr[vpss_chn].height);
                sample_print("ss_mpi_vpss_set_chn_attr failed with %#x  %d--%d\n", ret,chn_attr->width,chn_attr->height);
                goto disable_chn;
            }
            ret = ss_mpi_vpss_enable_chn(grp, vpss_chn);
            if (ret != TD_SUCCESS) {
                sample_print("ss_mpi_vpss_enable_chn failed with %#x\n", ret);
                goto disable_chn;
            }
        }
    }

#ifndef LOAD_CLUT4_BMP_ENABLE

    //------开辟扩展通道做PIP 画中画 小图通道 lym             绑定到PIP 画中画 大图通道
    ot_vpss_chn vpss_ext_PIP_chn = 4;
    vpss_ext_chn_PIP_attr.bind_chn = OT_VPSS_CHN1;
    vpss_ext_chn_PIP_attr.src_type = OT_EXT_CHN_SRC_TYPE_TAIL;
    vpss_ext_chn_PIP_attr.width = 640;     /* 1920: default width */
    vpss_ext_chn_PIP_attr.height = 360;//512;//360;    /* 1080: default height */
    vpss_ext_chn_PIP_attr.depth = 5;
    vpss_ext_chn_PIP_attr.video_format = OT_VIDEO_FORMAT_LINEAR;
    vpss_ext_chn_PIP_attr.dynamic_range = OT_DYNAMIC_RANGE_SDR8;
    vpss_ext_chn_PIP_attr.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
    vpss_ext_chn_PIP_attr.compress_mode = OT_COMPRESS_MODE_NONE;
    vpss_ext_chn_PIP_attr.frame_rate.src_frame_rate = -1;
    vpss_ext_chn_PIP_attr.frame_rate.dst_frame_rate = -1;

    //vpss_ext_chn_PIP_attr.width = media_cfg->vpss_chn_attr[0].width;
    //vpss_ext_chn_PIP_attr.height = media_cfg->vpss_chn_attr[0].height;
    //vpss_ext_chn_PIP_attr.pixel_format = OT_PIXEL_FORMAT_VY1UY0_PACKAGE_422;
    ret = ss_mpi_vpss_set_ext_chn_attr(grp, vpss_ext_PIP_chn, &vpss_ext_chn_PIP_attr);
    if (ret != TD_SUCCESS) {
        sample_print("set vpss ext_chn%d attr failed!\n", vpss_ext_PIP_chn);
        //goto vpss_fail;
    }

    ret = ss_mpi_vpss_enable_chn(grp, vpss_ext_PIP_chn);
    if (ret != TD_SUCCESS) {
        sample_print("enable vpss ext chn%d failed!\n", vpss_ext_PIP_chn);
        //goto vpss_fail;
    }
    //------开辟扩展通道做PIP 画中画 小图通道 lym        



    //------开辟扩展通道做放大 放大至 1280X1024
    ot_vpss_chn vpss_ext_VENC_chn = 5;
    vpss_ext_chn_VENC_attr.bind_chn = OT_VPSS_CHN1;
    vpss_ext_chn_VENC_attr.src_type = OT_EXT_CHN_SRC_TYPE_TAIL;
    vpss_ext_chn_VENC_attr.width = 1280;     /* 1920: default width */
    vpss_ext_chn_VENC_attr.height = 1024;    /* 1080: default height */
    vpss_ext_chn_VENC_attr.depth = 5;
    vpss_ext_chn_VENC_attr.video_format = OT_VIDEO_FORMAT_LINEAR;
    vpss_ext_chn_VENC_attr.dynamic_range = OT_DYNAMIC_RANGE_SDR8;
    vpss_ext_chn_VENC_attr.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
    vpss_ext_chn_VENC_attr.compress_mode = OT_COMPRESS_MODE_NONE;
    vpss_ext_chn_VENC_attr.frame_rate.src_frame_rate = -1;
    vpss_ext_chn_VENC_attr.frame_rate.dst_frame_rate = -1;

    //vpss_ext_chn_PIP_attr.width = media_cfg->vpss_chn_attr[0].width;
    //vpss_ext_chn_PIP_attr.height = media_cfg->vpss_chn_attr[0].height;
    //vpss_ext_chn_PIP_attr.pixel_format = OT_PIXEL_FORMAT_VY1UY0_PACKAGE_422;
    ret = ss_mpi_vpss_set_ext_chn_attr(grp, vpss_ext_PIP_chn, &vpss_ext_chn_PIP_attr);
    if (ret != TD_SUCCESS) {
        sample_print("set vpss ext_chn%d attr failed!\n", vpss_ext_PIP_chn);
        //goto vpss_fail;
    }

    ret = ss_mpi_vpss_enable_chn(grp, vpss_ext_PIP_chn);
    if (ret != TD_SUCCESS) {
        sample_print("enable vpss ext chn%d failed!\n", vpss_ext_PIP_chn);
        //goto vpss_fail;
    }
    //------开辟扩展通道做放大        
#endif



    
    return TD_SUCCESS;

disable_chn:
    for (i = i - 1; i >= 0; i--) {
        if (chn_enable[i] == TD_TRUE) {
            vpss_chn = i;
            ret = ss_mpi_vpss_disable_chn(grp, vpss_chn);
            if (ret != TD_SUCCESS) {
                sample_print("ss_mpi_vpss_disable_chn failed with %#x!\n", ret);
            }
        }
    }
    return TD_FAILURE;
}

static td_s32 sample_common_vpss_start_chn(ot_vpss_grp grp, const td_bool *chn_enable,
                                            ot_vpss_chn_attr *chn_attr, td_u32 chn_array_size)
{
    ot_vpss_chn vpss_chn;
    td_s32 ret, i;
    ot_vpss_ext_chn_attr vpss_ext_chn_attr;

    for (i = 0; i < (td_s32)chn_array_size; ++i) {
        if (chn_enable[i] == TD_TRUE) {
            vpss_chn = i;


#ifdef ENABLE_UVC_CAMERA
    	    if (vpss_chn == 0)
            {
                chn_attr->width  = 1920;
                chn_attr->height = 1080;
            }
#elif defined(LOAD_CLUT4_BMP_ENABLE)

#else
            if (vpss_chn == 0)
            {
                chn_attr->width  = 1920;
                chn_attr->height = 1080;
            }
            else if (vpss_chn == 1)
            {
                chn_attr->width  = 1920;
                chn_attr->height = 1080;
            }
            else if (vpss_chn == 2)
            {
                chn_attr->width  = 960;
                chn_attr->height = 512;
            }
            else if (vpss_chn == 3)
            {
                chn_attr->width  = 640;
                chn_attr->height = 360;
            }
#endif

	    sample_print("set vpss_chn:%d wxh=%dx%d\n",vpss_chn,chn_attr->width ,chn_attr->height);
            ret = ss_mpi_vpss_set_chn_attr(grp, vpss_chn, chn_attr);
            if (ret != TD_SUCCESS) {
                //sample_print("ss_mpi_vpss_set_chn_attr failed with %#x  %d--%d\n", ret,&chn_attr[vpss_chn].width,&chn_attr[vpss_chn].height);
                sample_print("ss_mpi_vpss_set_chn_attr failed with %#x  %d--%d\n", ret,chn_attr->width,chn_attr->height);
                goto disable_chn;
            }
            ret = ss_mpi_vpss_enable_chn(grp, vpss_chn);
            if (ret != TD_SUCCESS) {
                sample_print("ss_mpi_vpss_enable_chn failed with %#x\n", ret);
                goto disable_chn;
            }
        }
    }



        //------开辟扩展通道做电子放大用 lym             绑定到物理通道0

        ot_vpss_chn vpss_ext_chn = 4;

        vpss_ext_chn_attr.bind_chn = OT_VPSS_CHN0;
        vpss_ext_chn_attr.src_type = OT_EXT_CHN_SRC_TYPE_TAIL;
        vpss_ext_chn_attr.width = 1920;     /* 1920: default width */
        vpss_ext_chn_attr.height = 1080;    /* 1080: default height */
        vpss_ext_chn_attr.depth = 5;
        vpss_ext_chn_attr.video_format = OT_VIDEO_FORMAT_LINEAR;
        vpss_ext_chn_attr.dynamic_range = OT_DYNAMIC_RANGE_SDR8;
        vpss_ext_chn_attr.pixel_format = OT_PIXEL_FORMAT_YVU_SEMIPLANAR_420;
        vpss_ext_chn_attr.compress_mode = OT_COMPRESS_MODE_NONE;
        vpss_ext_chn_attr.frame_rate.src_frame_rate = -1;
        vpss_ext_chn_attr.frame_rate.dst_frame_rate = -1;

        //vpss_ext_chn_attr.width = media_cfg->vpss_chn_attr[0].width;
        //vpss_ext_chn_attr.height = media_cfg->vpss_chn_attr[0].height;
        //vpss_ext_chn_attr.pixel_format = OT_PIXEL_FORMAT_VY1UY0_PACKAGE_422;
        ret = ss_mpi_vpss_set_ext_chn_attr(grp, vpss_ext_chn, &vpss_ext_chn_attr);
        if (ret != TD_SUCCESS) {
            sample_print("set vpss ext_chn%d attr failed!\n", vpss_ext_chn);
            //goto vpss_fail;
        }

        ret = ss_mpi_vpss_enable_chn(grp, vpss_ext_chn);
        if (ret != TD_SUCCESS) {
            sample_print("enable vpss ext chn%d failed!\n", vpss_ext_chn);
            //goto vpss_fail;
        }
        //------开辟扩展通道做电子放大用 lym
        
    return TD_SUCCESS;

disable_chn:
    for (i = i - 1; i >= 0; i--) {
        if (chn_enable[i] == TD_TRUE) {
            vpss_chn = i;
            ret = ss_mpi_vpss_disable_chn(grp, vpss_chn);
            if (ret != TD_SUCCESS) {
                sample_print("ss_mpi_vpss_disable_chn failed with %#x!\n", ret);
            }
        }
    }
    return TD_FAILURE;
}

                                    
td_s32 sample_common_vpss_start_IR(ot_vpss_grp grp, const td_bool *chn_enable,
    const ot_vpss_grp_attr *grp_attr, const ot_vpss_chn_attr *chn_attr, td_u32 chn_array_size)
{
    td_s32 ret;
    if (chn_array_size < OT_VPSS_MAX_PHYS_CHN_NUM) {
        sample_print("array size(%u) of chn_enable and chn_attr need > %u!\n",
            chn_array_size, OT_VPSS_MAX_PHYS_CHN_NUM);
        return TD_FAILURE;
    }
    ret = ss_mpi_vpss_create_grp(grp, grp_attr);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_create_grp(grp:%d) failed with %#x!\n", grp, ret);
        return TD_FAILURE;
    }
    ret = ss_mpi_vpss_start_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_start_grp failed with %#x\n", ret);
        goto destroy_grp;
    }

    ret = sample_common_vpss_start_chn_IR(grp, chn_enable, chn_attr, OT_VPSS_MAX_PHYS_CHN_NUM);
    if (ret != TD_SUCCESS) {
        goto stop_grp;
    }

    return TD_SUCCESS;

    stop_grp:
    ret = ss_mpi_vpss_stop_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_stop_grp failed with %#x!\n", ret);
    }
    destroy_grp:
    ret = ss_mpi_vpss_destroy_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_destroy_grp failed with %#x!\n", ret);
    }
    return TD_FAILURE;
}

td_s32 sample_common_vpss_start(ot_vpss_grp grp, const td_bool *chn_enable,
    const ot_vpss_grp_attr *grp_attr, const ot_vpss_chn_attr *chn_attr, td_u32 chn_array_size)
{
    td_s32 ret;
    if (chn_array_size < OT_VPSS_MAX_PHYS_CHN_NUM) {
        sample_print("array size(%u) of chn_enable and chn_attr need > %u!\n",
            chn_array_size, OT_VPSS_MAX_PHYS_CHN_NUM);
        return TD_FAILURE;
    }
    ret = ss_mpi_vpss_create_grp(grp, grp_attr);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_create_grp(grp:%d) failed with %#x!\n", grp, ret);
        return TD_FAILURE;
    }
    ret = ss_mpi_vpss_start_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_start_grp failed with %#x\n", ret);
        goto destroy_grp;
    }

    ret = sample_common_vpss_start_chn(grp, chn_enable, chn_attr, OT_VPSS_MAX_PHYS_CHN_NUM);
    if (ret != TD_SUCCESS) {
        goto stop_grp;
    }

    return TD_SUCCESS;

stop_grp:
    ret = ss_mpi_vpss_stop_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_stop_grp failed with %#x!\n", ret);
    }
destroy_grp:
    ret = ss_mpi_vpss_destroy_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_destroy_grp failed with %#x!\n", ret);
    }
    return TD_FAILURE;
}

td_s32 sample_common_vpss_stop(ot_vpss_grp grp, const td_bool *chn_enable, td_u32 chn_array_size)
{
    td_s32 i;
    td_s32 ret;
    ot_vpss_chn vpss_chn;

    if (chn_array_size < OT_VPSS_MAX_PHYS_CHN_NUM) {
        sample_print("array size(%u) of chn_enable need > %u!\n", chn_array_size, OT_VPSS_MAX_PHYS_CHN_NUM);
        return TD_FAILURE;
    }

    for (i = 0; i < OT_VPSS_MAX_PHYS_CHN_NUM; ++i) {
        if (chn_enable[i] == TD_TRUE) {
            vpss_chn = i;
            ret = ss_mpi_vpss_disable_chn(grp, vpss_chn);
            if (ret != TD_SUCCESS) {
                sample_print("ss_mpi_vpss_disable_chn[%d, %d] failed with %#x!\n", grp, vpss_chn, ret);
            }
        }
    }

    ret = ss_mpi_vpss_stop_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_stop_grp failed with %#x!\n", ret);
    }

    ret = ss_mpi_vpss_destroy_grp(grp);
    if (ret != TD_SUCCESS) {
        sample_print("ss_mpi_vpss_destroy_grp failed with %#x!\n", ret);
    }

    return TD_SUCCESS;
}
